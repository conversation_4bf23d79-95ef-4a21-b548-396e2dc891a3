{"name": "backend-encuestas-tfi", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "compodoc": "npx compodoc -p tsconfig.json -d documentation -s -w", "deploy": "npm run build && pm2 start ecosystem.config.js", "deploy:dev": "npm run build && pm2 start ecosystem.config.js --only backend-encuestas-dev", "deploy:prod": "npm run build && pm2 start ecosystem.config.js --only backend-encuestas-prod --env production", "pm2:status": "pm2 status", "pm2:logs": "pm2 logs", "pm2:logs:dev": "pm2 logs backend-encuestas-dev", "pm2:logs:prod": "pm2 logs backend-encuestas-prod", "pm2:restart": "pm2 restart all", "pm2:restart:dev": "pm2 restart backend-encuestas-dev", "pm2:restart:prod": "pm2 restart backend-encuestas-prod", "pm2:stop": "pm2 stop all", "pm2:stop:dev": "pm2 stop backend-encuestas-dev", "pm2:stop:prod": "pm2 stop backend-encuestas-prod", "pm2:delete": "pm2 delete all", "pm2:delete:dev": "pm2 delete backend-encuestas-dev", "pm2:delete:prod": "pm2 delete backend-encuestas-prod", "pm2:monit": "pm2 monit", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.1.2", "@nestjs/typeorm": "^11.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "helmet": "^8.1.0", "node-fetch": "^3.3.2", "papaparse": "^5.5.3", "pg": "^8.14.1", "pm2": "^6.0.6", "qrcode": "^1.5.4", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "shortid": "^2.2.17", "typeorm": "^0.3.24", "uuid": "^11.1.0"}, "devDependencies": {"@compodoc/compodoc": "^1.1.26", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.15.18", "@types/papaparse": "^5.3.16", "@types/qrcode": "^1.5.5", "@types/supertest": "^6.0.2", "eslint": "^9.27.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}