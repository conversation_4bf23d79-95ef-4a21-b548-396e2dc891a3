// Definición de un enumerador para los tipos de respuesta permitidos en las encuestas
export enum TiposRespuestaEnum {
  ABIERTA = 'ABIERTA', // Respuesta abierta donde el usuario puede escribir libremente
  OPCION_MULTIPLE_SELECCION_SIMPLE = 'OPCION_MULTIPLE_SELECCION_SIMPLE', // Respuesta de opción múltiple con selección única
  OPCION_MULTIPLE_SELECCION_MULTIPLE = 'OPCION_MULTIPLE_SELECCION_MULTIPLE', // Respuesta de opción múltiple con selección múltiple
  VERDADERO_FALSO = 'VERDADERO_FALSO', // FUNCIÓN EXTRA: Respuesta de tipo verdadero/falso
}
