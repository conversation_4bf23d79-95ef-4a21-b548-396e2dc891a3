/* Estilos generales */
:root {
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --text-color: #333;
  --light-text: #fff;
  --dark-bg: #1a1a2e;
  --light-bg: #f8f9fa;
  --border-radius: 8px;
  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--text-color);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header elegante */
.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
  color: var(--light-text);
  padding: 1.5rem 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo {
  height: 50px;
  width: auto;
  border-radius: var(--border-radius);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.05) rotate(2deg);
}

.site-title {
  font-size: 2rem;
  font-weight: 800;
  margin: 0;
  color: #ffffff;
  animation: titleGlow 3s ease-in-out infinite alternate;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

@keyframes titleGlow {
  0% { filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5)); }
  100% { filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.8)); }
}

.main-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 1.5rem;
}

.main-nav a,
.main-nav .nav-button {
  color: var(--light-text);
  text-decoration: none;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  transition: all 0.3s ease;
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.main-nav .nav-button i {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.main-nav a:hover,
.main-nav .nav-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.main-nav .nav-button:hover i {
  transform: scale(1.2) rotate(5deg);
}

.main-nav a.active,
.main-nav .nav-button.active {
  background: rgba(255, 255, 255, 0.3);
  font-weight: 700;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.main-nav a.active::after,
.main-nav .nav-button.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 4px;
  background: linear-gradient(90deg, #ffffff, #f0f8ff);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.5);
}

/* Contenido principal */
.app-content {
  flex: 1;
  min-height: calc(100vh - 140px); /* Ajustado para footer más pequeño */
  border: none;
  margin: 0;
  padding: 0;
}

/* Estilos para cuando el header y footer están ocultos */
.app-content {
  transition: min-height 0.3s ease;
}

.app-content.fullscreen {
  min-height: 100vh;
  height: 100vh;
  overflow: auto;
}

/* Footer compacto y elegante */
.app-footer {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #533483 100%);
  background-size: 200% 200%;
  animation: footerGradientShift 8s ease infinite;
  color: var(--light-text);
  padding: 1.5rem 0;
  margin-top: 0;
  box-shadow: none;
  position: relative;
  overflow: hidden;
  border: none;
}

@keyframes footerGradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.footer-info h4 {
  font-size: 1.1rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  background: linear-gradient(45deg, #ffffff, #f0f8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-info p {
  font-size: 0.8rem;
  margin: 0;
  opacity: 0.8;
  color: #ffffff;
}

.footer-team {
  text-align: center;
  color: #ffffff;
}

.team-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #f093fb;
  display: block;
  margin-bottom: 0.5rem;
}

.team-names {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
  font-size: 0.75rem;
  max-width: 500px;
  margin: 0 auto;
}

.team-names span {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  white-space: nowrap;
}

.team-names span:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}



/* Responsive */
@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .site-title {
    font-size: 1.5rem;
  }

  .main-nav ul {
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.75rem;
  }

  .main-nav a,
  .main-nav .nav-button {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .main-nav .nav-button i {
    font-size: 0.9rem;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .footer-team {
    text-align: center;
  }

  .team-names {
    justify-content: center;
  }

  .team-names span {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
}

/* Aplica margen alrededor del cuadro de diálogo de confirmación de PrimeNG */
::ng-deep .p-confirmdialog {
  margin: 1rem;
}

/* Aplica margen y limita el ancho máximo de los diálogos de PrimeNG */
::ng-deep .p-dialog {
  margin: 1rem;
  max-width: 90vw; /* El ancho máximo es el 90% del viewport */
}

/* TOASTS MEJORADOS - CENTRADOS Y ANIMADOS */
::ng-deep .p-toast {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999 !important;
  max-width: 500px !important;
  width: auto !important;
}

::ng-deep .p-toast .p-toast-message {
  margin: 0.5rem 0 !important;
  border-radius: 15px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  animation: toastSlideIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
  min-width: 350px !important;
  max-width: 500px !important;
}

/* Animación de entrada para los toasts */
@keyframes toastSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.3) rotate(-10deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.1) rotate(5deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* Toast de éxito - Verde vibrante */
::ng-deep .p-toast .p-toast-message.p-toast-message-success {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 50%, #4CAF50 100%) !important;
  color: white !important;
  border-left: 5px solid #2E7D32 !important;
  animation: toastSlideIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55), successPulse 2s ease-in-out infinite !important;
}

/* Toast de error - Rojo vibrante */
::ng-deep .p-toast .p-toast-message.p-toast-message-error {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 50%, #f44336 100%) !important;
  color: white !important;
  border-left: 5px solid #c62828 !important;
  animation: toastSlideIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55), errorShake 0.5s ease-in-out !important;
}

/* Toast de advertencia - Naranja vibrante */
::ng-deep .p-toast .p-toast-message.p-toast-message-warn {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 50%, #ff9800 100%) !important;
  color: white !important;
  border-left: 5px solid #ef6c00 !important;
  animation: toastSlideIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55), warnBounce 1s ease-in-out infinite !important;
}

/* Toast de información - Azul vibrante */
::ng-deep .p-toast .p-toast-message.p-toast-message-info {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 50%, #2196F3 100%) !important;
  color: white !important;
  border-left: 5px solid #1565C0 !important;
  animation: toastSlideIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55), infoPulse 2s ease-in-out infinite !important;
}

/* Iconos de los toasts más grandes y animados */
::ng-deep .p-toast .p-toast-message .p-toast-message-icon {
  font-size: 2rem !important;
  margin-right: 1rem !important;
  animation: iconBounce 1s ease-in-out infinite !important;
}

/* Contenido del toast mejorado */
::ng-deep .p-toast .p-toast-message .p-toast-message-content {
  padding: 1.5rem !important;
  display: flex !important;
  align-items: center !important;
}

/* Texto del toast mejorado */
::ng-deep .p-toast .p-toast-message .p-toast-message-text {
  flex: 1 !important;
}

/* Título del toast más grande */
::ng-deep .p-toast .p-toast-message .p-toast-summary {
  font-size: 1.2rem !important;
  font-weight: 700 !important;
  margin-bottom: 0.5rem !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* Detalle del toast */
::ng-deep .p-toast .p-toast-message .p-toast-detail {
  font-size: 1rem !important;
  font-weight: 500 !important;
  line-height: 1.4 !important;
  opacity: 0.95 !important;
}

/* Botón de cerrar mejorado */
::ng-deep .p-toast .p-toast-message .p-toast-icon-close {
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 50% !important;
  width: 2rem !important;
  height: 2rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
}

::ng-deep .p-toast .p-toast-message .p-toast-icon-close:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: scale(1.1) !important;
}

/* ANIMACIONES PARA LOS TOASTS */

/* Animación de pulso para éxito */
@keyframes successPulse {
  0%, 100% {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2), 0 0 0 0 rgba(76, 175, 80, 0.4);
  }
  50% {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3), 0 0 0 10px rgba(76, 175, 80, 0.1);
  }
}

/* Animación de temblor para error */
@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
  20%, 40%, 60%, 80% { transform: translateX(3px); }
}

/* Animación de rebote para advertencia */
@keyframes warnBounce {
  0%, 100% {
    transform: translateY(0);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  }
}

/* Animación de pulso para información */
@keyframes infoPulse {
  0%, 100% {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2), 0 0 0 0 rgba(33, 150, 243, 0.4);
  }
  50% {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3), 0 0 0 10px rgba(33, 150, 243, 0.1);
  }
}

/* Animación de rebote para iconos */
@keyframes iconBounce {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-3px) scale(1.1);
  }
}

/* Responsive para toasts */
@media (max-width: 768px) {
  ::ng-deep .p-toast {
    max-width: 90vw !important;
  }

  ::ng-deep .p-toast .p-toast-message {
    min-width: 300px !important;
    max-width: 90vw !important;
  }

  ::ng-deep .p-toast .p-toast-message .p-toast-summary {
    font-size: 1.1rem !important;
  }

  ::ng-deep .p-toast .p-toast-message .p-toast-detail {
    font-size: 0.9rem !important;
  }
}

@media (max-width: 480px) {
  ::ng-deep .p-toast .p-toast-message {
    min-width: 280px !important;
  }

  ::ng-deep .p-toast .p-toast-message .p-toast-message-content {
    padding: 1rem !important;
  }

  ::ng-deep .p-toast .p-toast-message .p-toast-summary {
    font-size: 1rem !important;
  }

  ::ng-deep .p-toast .p-toast-message .p-toast-detail {
    font-size: 0.85rem !important;
  }

  ::ng-deep .p-toast .p-toast-message .p-toast-message-icon {
    font-size: 1.5rem !important;
  }
}