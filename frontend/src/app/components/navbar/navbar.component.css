:host{
  display: block;
  width: 100vw;
  min-width: 100vw;
  left: 0;
  top: 0;
}
.navbar-container {
  position: sticky;
  top: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  padding: 1rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.titulo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2d3748;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

::ng-deep .navbar-button .p-button{
  font-size: 1.1rem !important;
  font-weight: bold !important;
  background-color: rgb(0, 140, 255) !important;
  color: white !important;
}

::ng-deep .navbar-button .p-button:hover{
  background-color: white !important;
  color: rgb(0, 140, 255) !important;
  border: 2px solid rgb(0, 140, 255) !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}