import { Component } from '@angular/core';

/**
 * Componente TextoError.
 * Muestra mensajes de error personalizados en los formularios o vistas donde se requiera.
 * El contenido y el estilo se definen en el archivo de plantilla y CSS asociados.
 */
@Component({
  selector: 'app-texto-error',
  imports: [],
  templateUrl: './texto-error.component.html',
  styleUrl: './texto-error.component.css'
})
export class TextoErrorComponent {
  
}
