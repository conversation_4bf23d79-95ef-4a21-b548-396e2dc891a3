/* Host styles - Configuración principal del componente */
:host {
  min-height: calc(100vh - 70px); /* <PERSON><PERSON> mínima, pero permite que crezca */
  width: 100vw;
  display: block;
  margin: 0;
  padding: 0;
  /* Removemos overflow: hidden para permitir que se vea el footer */
}

/* Contenedor principal con gradiente de fondo mejorado */
.banner-container {
  min-height: calc(100vh - 70px);
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 0;
  padding: 2rem 1rem;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
    linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  box-sizing: border-box;
}

/* <PERSON><PERSON><PERSON> de puntos decorativo */
.banner-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25px 25px, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
  background-size: 50px 50px;
  animation: movePattern 20s linear infinite;
  z-index: 1;
}

@keyframes movePattern {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

/* Elementos decorativos de fondo mejorados */
.background-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-shape {
  position: absolute;
  animation: float 8s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), rgba(120, 219, 255, 0.3));
  border-radius: 50%;
  animation-delay: 0s;
  box-shadow: 0 8px 32px rgba(120, 219, 255, 0.3);
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  background: linear-gradient(135deg, rgba(255, 119, 198, 0.2), rgba(255, 255, 255, 0.1));
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  animation-delay: 2s;
  animation-name: floatMorph;
  box-shadow: 0 8px 32px rgba(255, 119, 198, 0.3);
}

.shape-3 {
  width: 60px;
  height: 60px;
  top: 80%;
  left: 20%;
  background: linear-gradient(225deg, rgba(120, 119, 198, 0.3), rgba(255, 255, 255, 0.2));
  border-radius: 50%;
  animation-delay: 4s;
  box-shadow: 0 8px 32px rgba(120, 119, 198, 0.3);
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 25%;
  background: linear-gradient(315deg, rgba(102, 126, 234, 0.2), rgba(255, 255, 255, 0.15));
  border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%;
  animation-delay: 1s;
  animation-name: floatRotate;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

/* Formas adicionales para más dinamismo */
.shape-1::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 3s ease-in-out infinite;
}

.shape-3::after {
  content: '';
  position: absolute;
  top: 10px;
  right: 10px;
  width: 15px;
  height: 15px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-30px) rotate(180deg) scale(1.1);
    opacity: 1;
  }
}

@keyframes floatMorph {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    opacity: 0.7;
  }
  25% {
    transform: translateY(-15px) rotate(90deg);
    border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
  }
  50% {
    transform: translateY(-25px) rotate(180deg);
    border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
    opacity: 1;
  }
  75% {
    transform: translateY(-15px) rotate(270deg);
    border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
  }
}

@keyframes floatRotate {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%;
    opacity: 0.7;
  }
  33% {
    transform: translateY(-20px) rotate(120deg) scale(1.05);
    border-radius: 37% 63% 46% 54% / 48% 55% 45% 52%;
  }
  66% {
    transform: translateY(-35px) rotate(240deg) scale(0.95);
    border-radius: 54% 46% 63% 37% / 52% 45% 55% 48%;
    opacity: 1;
  }
}

/* Círculos flotantes adicionales */
.floating-circle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  animation: floatCircle 10s ease-in-out infinite;
}

.circle-1 {
  width: 25px;
  height: 25px;
  top: 12%;
  left: 8%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(120, 219, 255, 0.2) 100%);
  animation-delay: 0s;
  box-shadow: 0 4px 20px rgba(120, 219, 255, 0.4);
}

.circle-2 {
  width: 35px;
  height: 35px;
  top: 25%;
  right: 12%;
  background: radial-gradient(circle, rgba(255, 119, 198, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
  animation-delay: 1.5s;
  box-shadow: 0 4px 20px rgba(255, 119, 198, 0.4);
}

.circle-3 {
  width: 18px;
  height: 18px;
  top: 45%;
  left: 5%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.4) 0%, transparent 70%);
  animation-delay: 3s;
  animation-duration: 8s;
}

.circle-4 {
  width: 42px;
  height: 42px;
  top: 65%;
  right: 8%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(120, 119, 198, 0.3) 100%);
  animation-delay: 4.5s;
  box-shadow: 0 6px 25px rgba(120, 119, 198, 0.3);
}

.circle-5 {
  width: 28px;
  height: 28px;
  top: 85%;
  left: 15%;
  background: radial-gradient(circle, rgba(255, 119, 198, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
  animation-delay: 6s;
  animation-duration: 12s;
}

.circle-6 {
  width: 22px;
  height: 22px;
  top: 35%;
  left: 85%;
  background: radial-gradient(circle, rgba(120, 219, 255, 0.35) 0%, transparent 80%);
  animation-delay: 7.5s;
  animation-duration: 9s;
}

.circle-7 {
  width: 38px;
  height: 38px;
  top: 55%;
  left: 3%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.25) 0%, rgba(102, 126, 234, 0.2) 100%);
  animation-delay: 2s;
  box-shadow: 0 5px 22px rgba(102, 126, 234, 0.35);
}

.circle-8 {
  width: 15px;
  height: 15px;
  top: 75%;
  right: 25%;
  background: radial-gradient(circle, rgba(255, 119, 198, 0.4) 0%, transparent 60%);
  animation-delay: 8s;
  animation-duration: 7s;
}

.circle-9 {
  width: 32px;
  height: 32px;
  top: 18%;
  left: 45%;
  background: radial-gradient(circle, rgba(120, 219, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
  animation-delay: 9.5s;
  animation-duration: 11s;
}

.circle-10 {
  width: 26px;
  height: 26px;
  top: 92%;
  right: 35%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(120, 119, 198, 0.25) 100%);
  animation-delay: 1s;
  animation-duration: 13s;
}

.circle-11 {
  width: 20px;
  height: 20px;
  top: 8%;
  right: 45%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.35) 0%, transparent 75%);
  animation-delay: 11s;
  animation-duration: 6s;
}

.circle-12 {
  width: 45px;
  height: 45px;
  top: 40%;
  left: 92%;
  background: radial-gradient(circle, rgba(255, 119, 198, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  animation-delay: 5s;
  box-shadow: 0 7px 30px rgba(255, 119, 198, 0.3);
}

@keyframes floatCircle {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-15px) translateX(10px) scale(1.1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-25px) translateX(-5px) scale(0.9);
    opacity: 1;
  }
  75% {
    transform: translateY(-10px) translateX(-15px) scale(1.05);
    opacity: 0.7;
  }
}

/* Formas orgánicas adicionales */
.organic-shape {
  position: absolute;
  pointer-events: none;
  animation: morphFloat 15s ease-in-out infinite;
}

.blob-1 {
  width: 60px;
  height: 60px;
  top: 22%;
  left: 25%;
  background: linear-gradient(135deg, rgba(255, 119, 198, 0.15) 0%, rgba(120, 219, 255, 0.2) 100%);
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  animation-delay: 0s;
  filter: blur(1px);
}

.blob-2 {
  width: 80px;
  height: 80px;
  top: 70%;
  right: 30%;
  background: linear-gradient(225deg, rgba(102, 126, 234, 0.18) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-radius: 40% 60% 70% 30% / 40% 70% 30% 60%;
  animation-delay: 5s;
  filter: blur(0.5px);
}

.blob-3 {
  width: 45px;
  height: 45px;
  top: 15%;
  right: 20%;
  background: linear-gradient(315deg, rgba(120, 219, 255, 0.2) 0%, rgba(255, 119, 198, 0.15) 100%);
  border-radius: 70% 30% 50% 50% / 30% 70% 50% 50%;
  animation-delay: 10s;
  animation-duration: 12s;
}

.blob-4 {
  width: 55px;
  height: 55px;
  top: 88%;
  left: 40%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 0%, rgba(120, 119, 198, 0.2) 100%);
  border-radius: 50% 50% 80% 20% / 50% 50% 20% 80%;
  animation-delay: 7s;
  animation-duration: 18s;
}

.blob-5 {
  width: 35px;
  height: 35px;
  top: 50%;
  left: 12%;
  background: linear-gradient(180deg, rgba(255, 119, 198, 0.25) 0%, rgba(102, 126, 234, 0.15) 100%);
  border-radius: 80% 20% 60% 40% / 20% 80% 40% 60%;
  animation-delay: 12s;
  animation-duration: 14s;
}

.blob-6 {
  width: 70px;
  height: 70px;
  top: 35%;
  right: 5%;
  background: linear-gradient(90deg, rgba(120, 219, 255, 0.18) 0%, rgba(255, 255, 255, 0.12) 100%);
  border-radius: 30% 70% 40% 60% / 70% 30% 60% 40%;
  animation-delay: 3s;
  animation-duration: 16s;
}

@keyframes morphFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    opacity: 0.6;
  }
  20% {
    transform: translateY(-10px) rotate(72deg) scale(1.1);
    border-radius: 40% 60% 50% 50% / 30% 70% 50% 50%;
    opacity: 0.8;
  }
  40% {
    transform: translateY(-20px) rotate(144deg) scale(0.9);
    border-radius: 70% 30% 60% 40% / 50% 50% 40% 60%;
    opacity: 1;
  }
  60% {
    transform: translateY(-15px) rotate(216deg) scale(1.05);
    border-radius: 50% 50% 70% 30% / 60% 40% 30% 70%;
    opacity: 0.7;
  }
  80% {
    transform: translateY(-5px) rotate(288deg) scale(0.95);
    border-radius: 30% 70% 40% 60% / 70% 30% 60% 40%;
    opacity: 0.9;
  }
}

/* Círculos con anillos */
.ring-circle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  animation: ringPulse 8s ease-in-out infinite;
}

.ring-1 {
  width: 50px;
  height: 50px;
  top: 28%;
  left: 65%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: radial-gradient(circle, transparent 60%, rgba(120, 219, 255, 0.2) 100%);
  animation-delay: 0s;
}

.ring-2 {
  width: 35px;
  height: 35px;
  top: 78%;
  left: 75%;
  border: 1.5px solid rgba(255, 119, 198, 0.4);
  background: radial-gradient(circle, transparent 50%, rgba(255, 119, 198, 0.15) 100%);
  animation-delay: 2s;
}

.ring-3 {
  width: 65px;
  height: 65px;
  top: 5%;
  left: 30%;
  border: 2.5px solid rgba(102, 126, 234, 0.25);
  background: radial-gradient(circle, transparent 70%, rgba(102, 126, 234, 0.1) 100%);
  animation-delay: 4s;
  animation-duration: 10s;
}

.ring-4 {
  width: 40px;
  height: 40px;
  top: 60%;
  left: 85%;
  border: 2px solid rgba(120, 119, 198, 0.35);
  background: radial-gradient(circle, transparent 55%, rgba(120, 119, 198, 0.18) 100%);
  animation-delay: 6s;
  animation-duration: 12s;
}

@keyframes ringPulse {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.3) rotate(180deg);
    opacity: 1;
  }
}

/* Formas geométricas adicionales */
.geometric-shape {
  position: absolute;
  pointer-events: none;
}

.triangle-1 {
  width: 0;
  height: 0;
  border-left: 25px solid transparent;
  border-right: 25px solid transparent;
  border-bottom: 43px solid rgba(255, 255, 255, 0.15);
  top: 15%;
  left: 70%;
  animation: floatSlow 12s ease-in-out infinite;
  filter: blur(1px);
}

.triangle-2 {
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-bottom: 26px solid rgba(120, 219, 255, 0.2);
  top: 70%;
  left: 80%;
  animation: floatSlow 10s ease-in-out infinite reverse;
  animation-delay: 3s;
}

.triangle-3 {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 35px solid rgba(255, 119, 198, 0.18);
  top: 45%;
  right: 10%;
  animation: floatSlow 14s ease-in-out infinite;
  animation-delay: 6s;
  filter: blur(0.5px);
}

.triangle-4 {
  width: 0;
  height: 0;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
  border-bottom: 21px solid rgba(102, 126, 234, 0.25);
  top: 82%;
  left: 55%;
  animation: floatSlow 8s ease-in-out infinite reverse;
  animation-delay: 9s;
}

.hexagon-1 {
  width: 40px;
  height: 23px;
  background: rgba(255, 119, 198, 0.2);
  position: relative;
  top: 25%;
  right: 5%;
  animation: spin 15s linear infinite;
}

.hexagon-1::before,
.hexagon-1::after {
  content: "";
  position: absolute;
  width: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
}

.hexagon-1::before {
  bottom: 100%;
  border-bottom: 12px solid rgba(255, 119, 198, 0.2);
}

.hexagon-1::after {
  top: 100%;
  border-top: 12px solid rgba(255, 119, 198, 0.2);
}

.hexagon-2 {
  width: 30px;
  height: 17px;
  background: rgba(102, 126, 234, 0.25);
  position: relative;
  top: 85%;
  left: 60%;
  animation: spin 20s linear infinite reverse;
  animation-delay: 5s;
}

.hexagon-2::before,
.hexagon-2::after {
  content: "";
  position: absolute;
  width: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
}

.hexagon-2::before {
  bottom: 100%;
  border-bottom: 9px solid rgba(102, 126, 234, 0.25);
}

.hexagon-2::after {
  top: 100%;
  border-top: 9px solid rgba(102, 126, 234, 0.25);
}

.hexagon-3 {
  width: 35px;
  height: 20px;
  background: rgba(120, 219, 255, 0.2);
  position: relative;
  top: 38%;
  left: 78%;
  animation: spin 18s linear infinite;
  animation-delay: 8s;
}

.hexagon-3::before,
.hexagon-3::after {
  content: "";
  position: absolute;
  width: 0;
  border-left: 17.5px solid transparent;
  border-right: 17.5px solid transparent;
}

.hexagon-3::before {
  bottom: 100%;
  border-bottom: 10px solid rgba(120, 219, 255, 0.2);
}

.hexagon-3::after {
  top: 100%;
  border-top: 10px solid rgba(120, 219, 255, 0.2);
}

/* Partículas brillantes */
.sparkle {
  position: absolute;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
  border-radius: 50%;
  animation: sparkleAnimation 3s ease-in-out infinite;
}

.sparkle-1 {
  width: 4px;
  height: 4px;
  top: 30%;
  left: 25%;
  animation-delay: 0s;
}

.sparkle-2 {
  width: 6px;
  height: 6px;
  top: 45%;
  right: 30%;
  animation-delay: 1s;
}

.sparkle-3 {
  width: 3px;
  height: 3px;
  top: 65%;
  left: 15%;
  animation-delay: 2s;
}

.sparkle-4 {
  width: 5px;
  height: 5px;
  top: 20%;
  right: 40%;
  animation-delay: 0.5s;
}

.sparkle-5 {
  width: 4px;
  height: 4px;
  top: 75%;
  right: 20%;
  animation-delay: 1.5s;
}

.sparkle-6 {
  width: 7px;
  height: 7px;
  top: 35%;
  left: 75%;
  animation-delay: 2.5s;
}

.sparkle-7 {
  width: 3px;
  height: 3px;
  top: 52%;
  right: 18%;
  animation-delay: 3s;
  animation-duration: 4s;
}

.sparkle-8 {
  width: 5px;
  height: 5px;
  top: 88%;
  left: 68%;
  animation-delay: 3.5s;
  animation-duration: 2.5s;
}

.sparkle-9 {
  width: 4px;
  height: 4px;
  top: 12%;
  left: 58%;
  animation-delay: 4s;
  animation-duration: 3.5s;
}

.sparkle-10 {
  width: 6px;
  height: 6px;
  top: 68%;
  right: 45%;
  animation-delay: 4.5s;
  animation-duration: 2.8s;
}

/* Ondas de fondo */
.wave {
  position: absolute;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.03) 50%, transparent 60%);
  animation: waveMove 25s linear infinite;
}

.wave-1 {
  top: -50%;
  left: -50%;
  animation-delay: 0s;
}

.wave-2 {
  top: -50%;
  left: -50%;
  animation-delay: 12.5s;
  background: linear-gradient(-45deg, transparent 40%, rgba(255, 255, 255, 0.02) 50%, transparent 60%);
}

.wave-3 {
  top: -50%;
  left: -50%;
  animation-delay: 8s;
  background: linear-gradient(90deg, transparent 35%, rgba(255, 255, 255, 0.025) 50%, transparent 65%);
  animation-duration: 30s;
}

/* Animaciones adicionales */
@keyframes floatSlow {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-15px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes sparkleAnimation {
  0%, 100% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes waveMove {
  0% {
    transform: translateX(-50%) translateY(-50%) rotate(0deg);
  }
  100% {
    transform: translateX(-50%) translateY(-50%) rotate(360deg);
  }
}

/* Contenido principal */
.banner-content {
  position: relative;
  z-index: 2;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 2rem 1.5rem;
  border-radius: 1.5rem;
  max-width: 700px;
  width: 90%;
  max-height: 90%;
  overflow-y: auto;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  animation: slideInUp 1s ease-out;
  box-sizing: border-box;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

/* Icono principal */
.main-icon {
  color: #667eea;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Título principal */
.banner-title {
  margin: 0;
  font-weight: 800;
  color: #2d3748;
  line-height: 1.2;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.title-line-1 {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: fadeInLeft 1s ease-out 0.3s both;
}

.title-line-2 {
  font-size: 1.8rem;
  color: #4a5568;
  animation: fadeInRight 1s ease-out 0.6s both;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Descripción */
.banner-description {
  font-size: 1rem;
  color: #718096;
  line-height: 1.5;
  max-width: 500px;
  margin: 0;
  animation: fadeIn 1s ease-out 0.9s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Grid de características */
.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  width: 100%;
  max-width: 400px;
  animation: fadeInUp 1s ease-out 1.2s both;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 0.5rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 0.8rem;
  transition: all 0.3s ease;
  cursor: default;
}

.feature-item:hover {
  transform: translateY(-5px);
  background: rgba(102, 126, 234, 0.15);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
}

.feature-icon {
  font-size: 1.5rem;
  filter: grayscale(0);
  transition: transform 0.3s ease;
}

.feature-item:hover .feature-icon {
  transform: scale(1.2);
}

.feature-item span {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.9rem;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Sección de información */
.info-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  animation: fadeInUp 1s ease-out 1.5s both;
  width: 100%;
}

.info-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  width: 100%;
  max-width: 600px;
}

.info-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.info-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.15);
  border-color: rgba(102, 126, 234, 0.3);
}

.info-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  animation: bounce 2s ease-in-out infinite;
}

.info-card:nth-child(1) .info-icon { animation-delay: 0s; }
.info-card:nth-child(2) .info-icon { animation-delay: 0.3s; }
.info-card:nth-child(3) .info-icon { animation-delay: 0.6s; }

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.info-card h3 {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.info-card p {
  font-size: 0.85rem;
  color: #718096;
  line-height: 1.4;
  margin: 0;
}

.info-subtitle {
  margin: 0;
  font-size: 1rem;
  color: #667eea;
  font-weight: 600;
  text-align: center;
  background: rgba(102, 126, 234, 0.1);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

/* Responsive design */
@media (max-width: 768px) {
  :host {
    height: calc(100vh - 60px);
  }

  .banner-content {
    padding: 1.5rem 1rem;
    margin: 0.5rem;
    border-radius: 1.2rem;
    max-height: 95%;
  }

  .content-wrapper {
    gap: 1rem;
  }

  .title-line-1 {
    font-size: 2rem;
  }

  .title-line-2 {
    font-size: 1.4rem;
  }

  .banner-description {
    font-size: 0.9rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 0.8rem;
    max-width: 250px;
  }

  .feature-item {
    padding: 0.8rem 0.5rem;
  }

  .info-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
    max-width: 300px;
  }

  .info-card {
    padding: 1rem;
  }

  .info-icon {
    font-size: 2rem;
  }

  .info-card h3 {
    font-size: 1rem;
  }

  .info-card p {
    font-size: 0.8rem;
  }

  .info-subtitle {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
  }

  .floating-shape,
  .floating-circle,
  .organic-shape,
  .geometric-shape,
  .ring-circle,
  .sparkle,
  .wave {
    display: none;
  }

  .main-icon svg {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 480px) {
  .banner-content {
    padding: 1rem 0.8rem;
    margin: 0.3rem;
  }

  .title-line-1 {
    font-size: 1.6rem;
  }

  .title-line-2 {
    font-size: 1.2rem;
  }

  .banner-description {
    font-size: 0.85rem;
  }

  .main-icon svg {
    width: 50px;
    height: 50px;
  }

  .feature-item span {
    font-size: 0.8rem;
  }

  .info-card {
    padding: 0.8rem;
  }

  .info-icon {
    font-size: 1.8rem;
  }

  .info-card h3 {
    font-size: 0.9rem;
  }

  .info-card p {
    font-size: 0.75rem;
  }

  .info-subtitle {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

::ng-deep .banner-button .p-button{
  font-size: 1.1rem !important;
  font-weight: bold !important;
  background-color: rgb(0, 140, 255) !important;
  color: white !important;

}

::ng-deep .banner-button .p-button:hover{
  background-color: white !important;
  color: rgb(0, 140, 255) !important;
  border: 2px solid rgb(0, 140, 255) !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}
}


