/* Host styles - Configuración principal del componente */
:host {

  min-height: calc(100vh - 70px);
  width: 100vw;
  display: block;
  position: relative;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #4b4eac 0%, #5a3a7f 50%, #8ecae6 100%);
}

/* Patrón de puntos decorativo */
:host::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25px 25px, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
  background-size: 50px 50px;
  animation: movePattern 20s linear infinite;
  z-index: 1;
  pointer-events: none;
}

@keyframes movePattern {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

/* Elementos decorativos de fondo */
.background-elements {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-shape {
  position: absolute;
  animation: float 8s ease-in-out infinite;
}

.shape-1 {
  width: 60px;
  height: 60px;
  top: 15%;
  left: 8%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), rgba(120, 219, 255, 0.3));
  border-radius: 50%;
  animation-delay: 0s;
  box-shadow: 0 8px 32px rgba(120, 219, 255, 0.3);
}

.shape-2 {
  width: 80px;
  height: 80px;
  top: 70%;
  right: 12%;
  background: linear-gradient(135deg, rgba(255, 119, 198, 0.2), rgba(255, 255, 255, 0.1));
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  animation-delay: 2s;
  animation-name: floatMorph;
  box-shadow: 0 8px 32px rgba(255, 119, 198, 0.3);
}

.shape-3 {
  width: 45px;
  height: 45px;
  top: 85%;
  left: 15%;
  background: linear-gradient(225deg, rgba(120, 119, 198, 0.3), rgba(255, 255, 255, 0.2));
  border-radius: 50%;
  animation-delay: 4s;
  box-shadow: 0 8px 32px rgba(120, 119, 198, 0.3);
}

.shape-4 {
  width: 70px;
  height: 70px;
  top: 8%;
  right: 20%;
  background: linear-gradient(315deg, rgba(102, 126, 234, 0.2), rgba(255, 255, 255, 0.15));
  border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%;
  animation-delay: 1s;
  animation-name: floatRotate;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-30px) rotate(180deg) scale(1.1);
    opacity: 1;
  }
}

@keyframes floatMorph {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    opacity: 0.7;
  }
  25% {
    transform: translateY(-15px) rotate(90deg);
    border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
  }
  50% {
    transform: translateY(-25px) rotate(180deg);
    border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
    opacity: 1;
  }
  75% {
    transform: translateY(-15px) rotate(270deg);
    border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
  }
}

@keyframes floatRotate {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%;
    opacity: 0.7;
  }
  33% {
    transform: translateY(-20px) rotate(120deg) scale(1.05);
    border-radius: 37% 63% 46% 54% / 48% 55% 45% 52%;
  }
  66% {
    transform: translateY(-35px) rotate(240deg) scale(0.95);
    border-radius: 54% 46% 63% 37% / 52% 45% 55% 48%;
    opacity: 1;
  }
}

/* Círculos flotantes */
.floating-circle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  animation: floatCircle 10s ease-in-out infinite;
}

.circle-1 {
  width: 20px;
  height: 20px;
  top: 25%;
  left: 5%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(120, 219, 255, 0.2) 100%);
  animation-delay: 0s;
}

.circle-2 {
  width: 28px;
  height: 28px;
  top: 45%;
  right: 8%;
  background: radial-gradient(circle, rgba(255, 119, 198, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
  animation-delay: 1.5s;
}

.circle-3 {
  width: 15px;
  height: 15px;
  top: 65%;
  left: 3%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.4) 0%, transparent 70%);
  animation-delay: 3s;
}

.circle-4 {
  width: 32px;
  height: 32px;
  top: 80%;
  right: 5%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(120, 119, 198, 0.3) 100%);
  animation-delay: 4.5s;
}

.circle-5 {
  width: 22px;
  height: 22px;
  top: 35%;
  left: 85%;
  background: radial-gradient(circle, rgba(255, 119, 198, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
  animation-delay: 6s;
}

.circle-6 {
  width: 18px;
  height: 18px;
  top: 55%;
  left: 92%;
  background: radial-gradient(circle, rgba(120, 219, 255, 0.35) 0%, transparent 80%);
  animation-delay: 7.5s;
}

.circle-7 {
  width: 25px;
  height: 25px;
  top: 12%;
  left: 40%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.25) 0%, rgba(102, 126, 234, 0.2) 100%);
  animation-delay: 2s;
}

.circle-8 {
  width: 30px;
  height: 30px;
  top: 90%;
  left: 60%;
  background: radial-gradient(circle, rgba(255, 119, 198, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  animation-delay: 5s;
}

@keyframes floatCircle {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-15px) translateX(10px) scale(1.1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-25px) translateX(-5px) scale(0.9);
    opacity: 1;
  }
  75% {
    transform: translateY(-10px) translateX(-15px) scale(1.05);
    opacity: 0.7;
  }
}


/* Elementos geométricas adicionales */
.geometric-shape {
  position: absolute;
  pointer-events: none;
}

.triangle-1 {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 35px solid rgba(255, 255, 255, 0.15);
  top: 20%;
  left: 75%;
  animation: floatSlow 12s ease-in-out infinite;
  filter: blur(1px);
}

.triangle-2 {
  width: 0;
  height: 0;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
  border-bottom: 21px solid rgba(120, 219, 255, 0.2);
  top: 75%;
  left: 85%;
  animation: floatSlow 10s ease-in-out infinite reverse;
  animation-delay: 3s;
}

.hexagon-1 {
  width: 30px;
  height: 17px;
  background: rgba(255, 119, 198, 0.2);
  position: relative;
  top: 30%;
  right: 3%;
  animation: spin 15s linear infinite;
}

.hexagon-1::before,
.hexagon-1::after {
  content: "";
  position: absolute;
  width: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
}

.hexagon-1::before {
  bottom: 100%;
  border-bottom: 9px solid rgba(255, 119, 198, 0.2);
}

.hexagon-1::after {
  top: 100%;
  border-top: 9px solid rgba(255, 119, 198, 0.2);
}

.hexagon-2 {
  width: 25px;
  height: 14px;
  background: rgba(102, 126, 234, 0.25);
  position: relative;
  top: 88%;
  left: 70%;
  animation: spin 20s linear infinite reverse;
  animation-delay: 5s;
}

.hexagon-2::before,
.hexagon-2::after {
  content: "";
  position: absolute;
  width: 0;
  border-left: 12.5px solid transparent;
  border-right: 12.5px solid transparent;
}

.hexagon-2::before {
  bottom: 100%;
  border-bottom: 7px solid rgba(102, 126, 234, 0.25);
}

.hexagon-2::after {
  top: 100%;
  border-top: 7px solid rgba(102, 126, 234, 0.25);
}

/* Formas orgánicas */
.organic-shape {
  position: absolute;
  pointer-events: none;
  animation: morphFloat 15s ease-in-out infinite;
}

.blob-1 {
  width: 50px;
  height: 50px;
  top: 25%;
  left: 20%;
  background: linear-gradient(135deg, rgba(255, 119, 198, 0.15) 0%, rgba(120, 219, 255, 0.2) 100%);
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  animation-delay: 0s;
  filter: blur(1px);
}

.blob-2 {
  width: 65px;
  height: 65px;
  top: 75%;
  right: 25%;
  background: linear-gradient(225deg, rgba(102, 126, 234, 0.18) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-radius: 40% 60% 70% 30% / 40% 70% 30% 60%;
  animation-delay: 5s;
  filter: blur(0.5px);
}

.blob-3 {
  width: 40px;
  height: 40px;
  top: 18%;
  right: 15%;
  background: linear-gradient(315deg, rgba(120, 219, 255, 0.2) 0%, rgba(255, 119, 198, 0.15) 100%);
  border-radius: 70% 30% 50% 50% / 30% 70% 50% 50%;
  animation-delay: 10s;
  animation-duration: 12s;
}

.blob-4 {
  width: 45px;
  height: 45px;
  top: 92%;
  left: 35%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 0%, rgba(120, 119, 198, 0.2) 100%);
  border-radius: 50% 50% 80% 20% / 50% 50% 20% 80%;
  animation-delay: 7s;
  animation-duration: 18s;
}

/* Círculos con anillos */
.ring-circle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  animation: ringPulse 8s ease-in-out infinite;
}

.ring-1 {
  width: 40px;
  height: 40px;
  top: 35%;
  left: 70%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: radial-gradient(circle, transparent 60%, rgba(120, 219, 255, 0.2) 100%);
  animation-delay: 0s;
}

.ring-2 {
  width: 30px;
  height: 30px;
  top: 82%;
  left: 80%;
  border: 1.5px solid rgba(255, 119, 198, 0.4);
  background: radial-gradient(circle, transparent 50%, rgba(255, 119, 198, 0.15) 100%);
  animation-delay: 2s;
}

/* Partículas brillantes */
.sparkle {
  position: absolute;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
  border-radius: 50%;
  animation: sparkleAnimation 3s ease-in-out infinite;
}

.sparkle-1 {
  width: 3px;
  height: 3px;
  top: 28%;
  left: 22%;
  animation-delay: 0s;
}

.sparkle-2 {
  width: 4px;
  height: 4px;
  top: 48%;
  right: 28%;
  animation-delay: 1s;
}

.sparkle-3 {
  width: 2px;
  height: 2px;
  top: 68%;
  left: 12%;
  animation-delay: 2s;
}

.sparkle-4 {
  width: 5px;
  height: 5px;
  top: 22%;
  right: 35%;
  animation-delay: 0.5s;
}

.sparkle-5 {
  width: 3px;
  height: 3px;
  top: 78%;
  right: 18%;
  animation-delay: 1.5s;
}

.sparkle-6 {
  width: 4px;
  height: 4px;
  top: 38%;
  left: 78%;
  animation-delay: 2.5s;
}

/* Ondas de fondo */
.wave {
  position: absolute;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.03) 50%, transparent 60%);
  animation: waveMove 25s linear infinite;
}

.wave-1 {
  top: -50%;
  left: -50%;
  animation-delay: 0s;
}

.wave-2 {
  top: -50%;
  left: -50%;
  animation-delay: 12.5s;
  background: linear-gradient(-45deg, transparent 40%, rgba(255, 255, 255, 0.02) 50%, transparent 60%);
}

/* Animaciones adicionales */
@keyframes floatSlow {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-15px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes morphFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    opacity: 0.6;
  }
  20% {
    transform: translateY(-10px) rotate(72deg) scale(1.1);
    border-radius: 40% 60% 50% 50% / 30% 70% 50% 50%;
    opacity: 0.8;
  }
  40% {
    transform: translateY(-20px) rotate(144deg) scale(0.9);
    border-radius: 70% 30% 60% 40% / 50% 50% 40% 60%;
    opacity: 1;
  }
  60% {
    transform: translateY(-15px) rotate(216deg) scale(1.05);
    border-radius: 50% 50% 70% 30% / 60% 40% 30% 70%;
    opacity: 0.7;
  }
  80% {
    transform: translateY(-5px) rotate(288deg) scale(0.95);
    border-radius: 30% 70% 40% 60% / 70% 30% 60% 40%;
    opacity: 0.9;
  }
}

@keyframes ringPulse {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.3) rotate(180deg);
    opacity: 1;
  }
}

@keyframes sparkleAnimation {
  0%, 100% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes waveMove {
  0% {
    transform: translateX(-50%) translateY(-50%) rotate(0deg);
  }
  100% {
    transform: translateX(-50%) translateY(-50%) rotate(360deg);
  }
}




/* Contenedor principal */
.resultados-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

/* Estado de carga */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}

.loading-spinner {
  margin-bottom: 1rem;
}

.loading-container h3 {
  color: white;
  font-size: 1.5rem;
  margin: 0;
}

/* Estado de error */
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 2rem;
}

.error-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 3rem;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.error-card h2 {
  color: #e74c3c;
  font-size: 1.8rem;
  margin-bottom: 1rem;
}

.error-card p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

/* Contenido de los resultados */
.resultados-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* Encabezado de los resultados */
.resultados-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.resultados-header h1 {
  color: #333;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.resultados-header h2 {
  color: #555;
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.resultados-descripcion {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
}

/* Preguntas y resultados */
.preguntas-resultados {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.pregunta-resultado-container {
  margin-bottom: 1.5rem;
}

/* Tarjeta de pregunta resultado */
::ng-deep .pregunta-resultado-card .p-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: none;
  transition: all 0.3s ease;
}

::ng-deep .pregunta-resultado-card .p-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

::ng-deep .pregunta-resultado-card .p-card-body {
  padding: 2rem;
}

/* Encabezado de pregunta resultado */
.pregunta-resultado-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 15px 15px 0 0;
}

.pregunta-numero {
  font-weight: 700;
  font-size: 1.2rem;
}

.pregunta-tipo {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: capitalize;
}

/* Título de pregunta */
.pregunta-titulo {
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  line-height: 1.4;
}

/* Contenido de resultados */
.pregunta-resultados-content {
  margin-top: 1rem;
}

/* Respuestas abiertas */
.respuestas-abiertas h4 {
  color: #667eea;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.respuesta-abierta-item {
  background: #f8f9fa;
  border-left: 4px solid #667eea;
  padding: 1rem;
  margin-bottom: 0.8rem;
  border-radius: 0 8px 8px 0;
}

.respuesta-abierta-item p {
  margin: 0;
  color: #333;
  line-height: 1.5;
}

/* Respuestas de opciones */
.respuestas-opciones h4 {
  color: #667eea;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.opciones-estadisticas {
  margin-bottom: 2rem;
}

.opcion-estadistica {
  margin-bottom: 1rem;
}

.opcion-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.opcion-texto {
  font-weight: 600;
  color: #333;
  flex: 1;
}

.opcion-cantidad {
  color: #666;
  font-size: 0.9rem;
}

.opcion-porcentaje {
  color: #667eea;
  font-weight: 600;
  font-size: 1rem;
}

.opcion-barra {
  background: #e9ecef;
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
}

.opcion-progreso {
  background: linear-gradient(135deg, #667eea, #764ba2);
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Gráfico */
.grafico-container {
  margin-top: 2rem;
  text-align: center;
}

.grafico-container h5 {
  color: #667eea;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

::ng-deep .resultado-chart {
  max-width: 400px;
  margin: 0 auto;
}

/* Sin respuestas */
.no-respuestas {
  color: #999;
  font-style: italic;
  text-align: center;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 1rem 0;
}

/* Acciones */
.acciones-container {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 3rem;
  padding: 2rem 0;
  flex-wrap: wrap;
}

::ng-deep .accion-button {
  background: linear-gradient(135deg, #667eea, #764ba2) !important;
  border: none !important;
  border-radius: 25px !important;
  padding: 1rem 2rem !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
}

::ng-deep .accion-button:hover:not(:disabled) {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4) !important;
}

/* Botón CSV específico */
::ng-deep .csv-button {
  background: linear-gradient(135deg, #28a745, #20c997) !important;
  border: none !important;
  border-radius: 25px !important;
  padding: 1rem 2rem !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3) !important;
}

::ng-deep .csv-button:hover:not(:disabled) {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4) !important;
  background: linear-gradient(135deg, #20c997, #28a745) !important;
}

::ng-deep .csv-button:disabled {
  opacity: 0.6 !important;
  transform: none !important;
  cursor: not-allowed !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .resultados-container {
    padding: 1rem;
  }

  .resultados-header {
    padding: 1.5rem;
  }

  .resultados-header h1 {
    font-size: 2rem;
  }

  .resultados-header h2 {
    font-size: 1.5rem;
  }

  ::ng-deep .pregunta-resultado-card .p-card-body {
    padding: 1.5rem;
  }

  .pregunta-resultado-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .pregunta-titulo {
    font-size: 1.2rem;
  }

  .opcion-info {
    flex-direction: column;
    align-items: flex-start;
  }

  ::ng-deep .resultado-chart {
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .resultados-container {
    padding: 0.5rem;
  }

  .resultados-header {
    padding: 1rem;
  }

  .resultados-header h1 {
    font-size: 1.8rem;
  }

  .resultados-header h2 {
    font-size: 1.3rem;
  }

  .resultados-descripcion {
    font-size: 1rem;
  }

  ::ng-deep .pregunta-resultado-card .p-card-body {
    padding: 1rem;
  }

  .pregunta-titulo {
    font-size: 1.1rem;
  }

  .pregunta-tipo {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
  }

  .respuesta-abierta-item {
    padding: 0.8rem;
  }

  ::ng-deep .resultado-chart {
    max-width: 250px;
  }

  ::ng-deep .accion-button {
    padding: 0.8rem 1.5rem !important;
    font-size: 1rem !important;
  }
}