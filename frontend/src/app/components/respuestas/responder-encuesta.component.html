<!-- Elementos decorativos de fondo -->
<div class="background-elements">
  <!-- Formas flotantes principales -->
  <div class="floating-shape shape-1"></div>
  <div class="floating-shape shape-2"></div>
  <div class="floating-shape shape-3"></div>
  <div class="floating-shape shape-4"></div>

  <!-- Círculos flotantes adicionales -->
  <div class="floating-circle circle-1"></div>
  <div class="floating-circle circle-2"></div>
  <div class="floating-circle circle-3"></div>
  <div class="floating-circle circle-4"></div>
  <div class="floating-circle circle-5"></div>
  <div class="floating-circle circle-6"></div>
  <div class="floating-circle circle-7"></div>
  <div class="floating-circle circle-8"></div>

  <!-- Formas orgánicas adicionales -->
  <div class="organic-shape blob-1"></div>
  <div class="organic-shape blob-2"></div>
  <div class="organic-shape blob-3"></div>
  <div class="organic-shape blob-4"></div>

  <!-- Elementos adicionales para más dinamismo -->
  <div class="geometric-shape triangle-1"></div>
  <div class="geometric-shape triangle-2"></div>
  <div class="geometric-shape hexagon-1"></div> 
  <div class="geometric-shape hexagon-2"></div>

  <!-- Círculos con anillos -->
  <div class="ring-circle ring-1"></div>
  <div class="ring-circle ring-2"></div>

  <!-- Partículas brillantes -->
  <div class="sparkle sparkle-1"></div>
  <div class="sparkle sparkle-2"></div>
  <div class="sparkle sparkle-3"></div>
  <div class="sparkle sparkle-4"></div>
  <div class="sparkle sparkle-5"></div>
  <div class="sparkle sparkle-6"></div>

  <!-- Ondas de fondo -->
  <div class="wave wave-1"></div>
  <div class="wave wave-2"></div>
</div>

<div class="encuesta-container">
  <p-toast></p-toast>

  <div *ngIf="cargando" class="loading-container">
    <p-progressSpinner></p-progressSpinner>
    <p>Cargando encuesta...</p>
  </div>

  <!-- Alerta para encuesta deshabilitada -->
  <div *ngIf="encuestaDeshabilitada && !cargando" class="encuesta-deshabilitada-container">
    <div class="encuesta-deshabilitada-card">
      <p-message
        severity="error"
        [text]="mensajeEncuestaDeshabilitada"
        styleClass="encuesta-deshabilitada-message">
      </p-message>
      <div class="encuesta-deshabilitada-content">
        <i class="pi pi-exclamation-triangle encuesta-deshabilitada-icon"></i>
        <h3>Encuesta no disponible</h3>
        <p>{{ mensajeEncuestaDeshabilitada }}</p>
        <p class="redirect-message">Serás redirigido al inicio en unos segundos...</p>
        <p-button
          label="Ir al inicio ahora"
          icon="pi pi-home"
          (click)="router.navigate(['/'])"
          styleClass="p-button-outlined">
        </p-button>
      </div>
    </div>
  </div>

  <div *ngIf="!cargando && encuesta" class="encuesta-content">
    <!-- Alerta para encuesta deshabilitada durante el llenado -->
    <div *ngIf="encuestaDeshabilitada" class="encuesta-deshabilitada-overlay">
      <p-message
        severity="error"
        [text]="mensajeEncuestaDeshabilitada"
        styleClass="encuesta-deshabilitada-alert">
      </p-message>
      <div class="encuesta-deshabilitada-actions">
        <p class="redirect-message">Serás redirigido al inicio en unos segundos...</p>
        <p-button
          label="Ir al inicio ahora"
          icon="pi pi-home"
          (click)="router.navigate(['/'])"
          styleClass="p-button-outlined p-button-sm">
        </p-button>
      </div>
    </div>

    <p-card [ngClass]="{'encuesta-disabled': encuestaDeshabilitada}">
      <ng-template pTemplate="header">
        <div class="encuesta-header">
          <h2>{{ encuesta.nombre }}</h2>
          <p *ngIf="encuesta.descripcion" class="encuesta-descripcion">
            {{ encuesta.descripcion }}
          </p>
        </div>
      </ng-template>

      <form (ngSubmit)="enviarRespuestas()" #form="ngForm">
        <div *ngFor="let pregunta of encuesta.preguntas" class="pregunta-container">
          <div class="pregunta-header">
            <label class="titulo-pregunta">
              {{ pregunta.numero }}. {{ pregunta.texto }}
              <span *ngIf="pregunta.obligatoria" class="obligatoria">*</span>
            </label>
          </div>

          <div class="pregunta-content">
            <div *ngIf="esPreguntaAbierta(pregunta)" class="pregunta-abierta">
              <textarea
                rows="4"
                [(ngModel)]="getRespuestaPorPregunta(pregunta.numero).texto"
                (ngModelChange)="onTextoChange(pregunta, $event)"
                [name]="'pregunta-texto-' + pregunta.numero"
                placeholder="Escribí tu respuesta aquí..."
                class="w-full textarea-custom"
                [required]="pregunta.obligatoria">
              </textarea>
            </div>

            <div *ngIf="esPreguntaSeleccionSimple(pregunta)" class="pregunta-seleccion-simple">
              <div *ngFor="let opcion of pregunta.opciones" class="opcion-radio">
                <p-radioButton
                  [inputId]="'radio-' + pregunta.numero + '-' + opcion.id"
                  [name]="'pregunta-' + pregunta.numero"
                  [value]="opcion.id"
                  [(ngModel)]="getRespuestaPorPregunta(pregunta.numero).opciones[0]"
                  (onChange)="onRadioChange(pregunta, opcion.id)">
                </p-radioButton>
                <label [for]="'radio-' + pregunta.numero + '-' + opcion.id" class="opcion-label">
                  {{ opcion.texto }}
                </label>
              </div>
            </div>

            <div *ngIf="esPreguntaSeleccionMultiple(pregunta)" class="pregunta-seleccion-multiple">
              <div *ngFor="let opcion of pregunta.opciones" class="opcion-checkbox">
                <p-checkbox
                  [inputId]="'chk-' + pregunta.numero + '-' + opcion.id"
                  [value]="opcion.id"
                  [name]="'pregunta-' + pregunta.numero"
                  [ngModel]="isOpcionSeleccionada(pregunta.numero, opcion.id)"
                  (onChange)="onCheckboxChange(pregunta, opcion.id, $event.checked)"
                  [binary]="true">
                </p-checkbox>
                <label [for]="'chk-' + pregunta.numero + '-' + opcion.id" class="opcion-label">
                  {{ opcion.texto }}
                </label>
              </div>
            </div>

            <div *ngIf="esPreguntaVerdaderoFalso(pregunta)" class="pregunta-verdadero-falso">
              <div *ngFor="let opcion of pregunta.opciones" class="opcion-radio">
                <p-radioButton
                  [inputId]="'radio-' + pregunta.numero + '-' + opcion.numero"
                  [name]="'pregunta-' + pregunta.numero"
                  [value]="opcion.id"
                  [(ngModel)]="getRespuestaPorPregunta(pregunta.numero).opciones[0]"
                  (onChange)="onRadioChange(pregunta, opcion.numero)">
                </p-radioButton>
                <label [for]="'radio-' + pregunta.numero + '-' + opcion.numero" class="opcion-label">
                  {{ opcion.texto }}
                </label>
              </div>
            </div>
          </div>
        </div>

        <div class="form-actions">
          <p-button
            type="button"
            label="Vista Previa"
            icon="pi pi-eye"
            severity="secondary"
            (onClick)="abrirVistaPrevia()"
            [disabled]="enviando || encuestaDeshabilitada"
            styleClass="p-button-lg mr-2">
          </p-button>

          <p-button
            type="submit"
            [label]="encuestaDeshabilitada ? 'Encuesta no disponible' : 'Enviar Respuestas'"
            [icon]="encuestaDeshabilitada ? 'pi pi-ban' : 'pi pi-check'"
            [loading]="enviando"
            [disabled]="enviando || encuestaDeshabilitada"
            [styleClass]="encuestaDeshabilitada ? 'p-button-lg p-button-danger' : 'p-button-lg'">
          </p-button>
        </div>
      </form>
    </p-card>
  </div>
<!--
  <div *ngIf="!cargando && !encuesta" class="error-container">
    <p-card>
      <div class="error-content">
        <i class="pi pi-exclamation-triangle error-icon"></i>
        <h3>¡Ups!</h3>
        <p>'La encuesta ha vencido y ya no acepta respuestas</p>
      </div>
    </p-card>
  </div>
-->
  <p-dialog
    header="Vista Previa de Respuestas"
    [(visible)]="mostrarDialogVistaPrevia"
    [modal]="true"
    [style]="{width: '70vw', maxWidth: '800px'}"
    [closable]="true"
    [draggable]="false">

    <div class="vista-previa-content">
      <p class="mb-4">Por favor, revisá tus respuestas antes de enviar:</p>

      <div *ngFor="let pregunta of encuesta?.preguntas" class="pregunta-previa mb-4">
        <div class="pregunta-titulo font-semibold mb-2">
          {{ pregunta.numero }}. {{ pregunta.texto }}
          <span *ngIf="pregunta.obligatoria" class="text-red-500">*</span>
        </div>
        <div class="respuesta-previa bg-gray-50 p-3 rounded border-l-4 border-blue-400">
          {{ obtenerTextoRespuesta(pregunta) }}
        </div>
      </div>
    </div>

    <ng-template pTemplate="footer">
      <p-button
        label="Volver a Editar"
        icon="pi pi-pencil"
        severity="secondary"
        (onClick)="mostrarDialogVistaPrevia = false">
      </p-button>
      <p-button
        label="Enviar Respuestas"
        icon="pi pi-check"
        (onClick)="confirmarEnvio(); mostrarDialogVistaPrevia = false"
        styleClass="ml-2">
      </p-button>
    </ng-template>
  </p-dialog>

  <p-dialog
    header="Confirmar Envío"
    [(visible)]="mostrarDialogConfirmacion"
    [modal]="true"
    [style]="{width: '450px'}"
    [closable]="true"
    [draggable]="false">

    <div class="confirmacion-content">
      <div class="flex align-items-center mb-4">
        <i class="pi pi-question-circle text-4xl text-blue-500 mr-3"></i>
        <div>
          <p class="text-lg font-semibold mb-2">¿Estás seguro de enviar tus respuestas?</p>
          <p class="text-gray-600">Una vez enviadas, no podrás modificarlas.</p>
        </div>
      </div>

      <p-divider></p-divider>

      <div class="estadisticas mt-4">
        <p><strong>Preguntas respondidas:</strong> {{ contarRespuestasCompletadas() }} de {{ encuesta?.preguntas?.length }}</p>
        <p><strong>Encuesta:</strong> {{ encuesta?.nombre }}</p>
      </div>
    </div>

    <ng-template pTemplate="footer">
      <p-button
        label="Cancelar"
        icon="pi pi-times"
        severity="secondary"
        (onClick)="mostrarDialogConfirmacion = false">
      </p-button>
      <p-button
        label="Confirmar Envío"
        icon="pi pi-check"
        [loading]="enviando"
        (onClick)="enviarRespuestasFinal()"
        styleClass="ml-2">
      </p-button>
    </ng-template>
  </p-dialog>

  <p-dialog
    header="Error"
    [visible]="mostrarDialogError && !encuestaDeshabilitada"
    (visibleChange)="mostrarDialogError = $event"
    [modal]="true"
    [style]="{width: '500px'}"
    [closable]="true"
    [draggable]="false">

    <div class="error-dialog-content">
      <div class="flex align-items-center mb-4">
        <i class="pi pi-exclamation-triangle text-4xl text-red-500 mr-3"></i>
        <div>
          <p class="text-lg font-semibold mb-2">Hubo un problema</p>
          <p class="text-gray-600">No se pudieron procesar tus respuestas.</p>
        </div>
      </div>

      <p-divider></p-divider>

      <div class="error-detalles mt-4 p-3 bg-red-50 border-l-4 border-red-400 rounded">
        <p class="text-sm text-red-700">{{ errorDetallado }}</p>
      </div>
    </div>

    <ng-template pTemplate="footer">
      <p-button
        label="Cerrar"
        icon="pi pi-times"
        severity="secondary"
        (onClick)="mostrarDialogError = false">
      </p-button>
      <p-button
        label="Reintentar"
        icon="pi pi-refresh"
        (onClick)="reintentarEnvio()"
        styleClass="ml-2">
      </p-button>
    </ng-template>
  </p-dialog>
</div>