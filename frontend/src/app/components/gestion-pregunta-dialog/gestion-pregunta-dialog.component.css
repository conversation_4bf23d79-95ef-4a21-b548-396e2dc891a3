::ng-deep .p-dialog{
  background-color: rgb(255, 255, 255) !important;
  color: #333 !important;
}

#contenedor-creacion-pregunta {
  margin-top: 0.3rem;
  display: flex;
  flex-direction: column;
  background-color: rgb(255, 255, 255);
  color: #333;
}

#dropdown-tipos-pregunta {
  margin-bottom: 0.5rem;
  margin-top: 0.5rem;
}

#contenedor-boton-finalizar-gestion {
  margin-top: 1rem;
  width: 100%;
  display: flex;
  justify-content: center;
}

.contenedor-opcion {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 100%;
  margin: 5px;
}

ul {
  width: 100%;
  margin: 0;
}

#heading-opciones {
  margin: 0;
  font-size: 24px;
  color:#333;
}

#opciones-vacio {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.texto-opcion {
  max-width: 60%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  color: #333;
  margin-left: 0.5rem;
}

#boton-eliminar-opcion {
  margin-right: 0.5rem;
}

ul {
  max-height: 8rem;
  overflow: auto;
  margin-top: 1rem;
  margin-bottom: 1rem;
  padding-left: 0;
  list-style: none;
  border: 1px solid #a3c8f2; /* borde celeste claro */
  background-color: #ffffff;
  border-radius: 6px;
}

#botonera-opciones{
  display: flex;
  justify-content: flex-end;
  flex-wrap: wrap;
}
