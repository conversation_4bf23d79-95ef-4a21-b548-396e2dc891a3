node_modules
dist
**/dist
**/node_modules
**.env

logs
**.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
**.launch
.settings/

**/tmp
**/out-tsc
**/bazel-out

.idea/
.project
.classpath
.c9/
**.launch
.settings/
**.sublime-workspace

**.vscode/*
**!.vscode/settings.json
**!.vscode/tasks.json
**!.vscode/launch.json
**!.vscode/extensions.json
**.history/*

**/.angular/cache
**.sass-cache/
**/connect.lock
**/coverage
**/libpeerconnection.log
**testem.log
**/typings

**.DS_Store
**Thumbs.db

**/documentation
.vscode